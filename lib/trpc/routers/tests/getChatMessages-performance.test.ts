/**
 * Performance test for getChatMessages optimization
 * 
 * This test compares the performance of the optimized getChatMessages query
 * against the previous implementation to verify improvements.
 */

import { describe, it, expect, beforeAll, afterAll } from "bun:test";
import { db } from "@/lib/db";
import { chats, chatParticipants, messages, users, cats } from "@/lib/db/schema";
import { eq } from "drizzle-orm";

// Mock data for testing
const testData = {
	users: [] as any[],
	cats: [] as any[],
	chats: [] as any[],
	messages: [] as any[],
};

describe("getChatMessages Performance Tests", () => {
	beforeAll(async () => {
		// Create test users
		const [user1, user2] = await db
			.insert(users)
			.values([
				{
					name: "Test User 1",
					email: "<EMAIL>",
					slug: "test-user-1",
				},
				{
					name: "Test User 2", 
					email: "<EMAIL>",
					slug: "test-user-2",
				},
			])
			.returning();

		testData.users = [user1, user2];

		// Create test cat
		const [cat] = await db
			.insert(cats)
			.values({
				name: "Test Cat",
				slug: "test-cat",
				gender: "male",
				age: 2,
				description: "A test cat for performance testing",
				userId: user1.id,
			})
			.returning();

		testData.cats = [cat];

		// Create test chat
		const [chat] = await db
			.insert(chats)
			.values({
				catId: cat.id,
			})
			.returning();

		testData.chats = [chat];

		// Add participants
		await db.insert(chatParticipants).values([
			{ chatId: chat.id, userId: user1.id },
			{ chatId: chat.id, userId: user2.id },
		]);

		// Create test messages (50 messages for pagination testing)
		const messageData = Array.from({ length: 50 }, (_, i) => ({
			chatId: chat.id,
			userId: i % 2 === 0 ? user1.id : user2.id,
			content: `Test message ${i + 1}`,
			status: i < 10 ? ("sent" as const) : ("read" as const),
		}));

		const insertedMessages = await db
			.insert(messages)
			.values(messageData)
			.returning();

		testData.messages = insertedMessages;
	});

	afterAll(async () => {
		// Clean up test data
		if (testData.messages.length > 0) {
			await db.delete(messages).where(
				eq(messages.chatId, testData.chats[0].id)
			);
		}
		if (testData.chats.length > 0) {
			await db.delete(chatParticipants).where(
				eq(chatParticipants.chatId, testData.chats[0].id)
			);
			await db.delete(chats).where(eq(chats.id, testData.chats[0].id));
		}
		if (testData.cats.length > 0) {
			await db.delete(cats).where(eq(cats.id, testData.cats[0].id));
		}
		if (testData.users.length > 0) {
			await db.delete(users).where(eq(users.id, testData.users[0].id));
			await db.delete(users).where(eq(users.id, testData.users[1].id));
		}
	});

	it("should fetch messages efficiently with optimized query", async () => {
		const startTime = performance.now();
		
		// Simulate the optimized getChatMessages query structure
		const chatId = testData.chats[0].id;
		const userId = testData.users[0].id;

		// Step 1: Check participant access (optimized)
		const isParticipant = await db.query.chatParticipants.findFirst({
			where: eq(chatParticipants.chatId, chatId),
			columns: { id: true },
		});

		expect(isParticipant).toBeDefined();

		// Step 2: Get chat details with selective fields
		const chat = await db.query.chats.findFirst({
			where: eq(chats.id, chatId),
			columns: {
				id: true,
				catId: true,
				createdAt: true,
			},
			with: {
				cat: {
					columns: {
						id: true,
						slug: true,
						name: true,
						userId: true,
						status: true,
					},
				},
				participants: {
					columns: {
						userId: true,
					},
					with: {
						user: {
							columns: {
								id: true,
								slug: true,
								name: true,
								image: true,
								role: true,
							},
						},
					},
				},
			},
		});

		expect(chat).toBeDefined();

		// Step 3: Get messages with selective fields
		const chatMessages = await db.query.messages.findMany({
			where: eq(messages.chatId, chatId),
			limit: 20,
			columns: {
				id: true,
				content: true,
				createdAt: true,
				status: true,
				userId: true,
			},
			with: {
				user: {
					columns: {
						id: true,
						name: true,
						image: true,
					},
				},
			},
		});

		const duration = performance.now() - startTime;

		expect(chatMessages).toHaveLength(20);
		expect(duration).toBeLessThan(100); // Should complete in under 100ms

		console.log(`Optimized getChatMessages completed in ${duration.toFixed(2)}ms`);
	});

	it("should handle pagination efficiently", async () => {
		const startTime = performance.now();
		
		const chatId = testData.chats[0].id;
		
		// Get first page
		const firstPage = await db.query.messages.findMany({
			where: eq(messages.chatId, chatId),
			limit: 10,
			columns: {
				id: true,
				content: true,
				createdAt: true,
				status: true,
				userId: true,
			},
		});

		// Get second page using cursor
		const cursor = firstPage[firstPage.length - 1].createdAt;
		const secondPage = await db.query.messages.findMany({
			where: eq(messages.chatId, chatId),
			limit: 10,
			columns: {
				id: true,
				content: true,
				createdAt: true,
				status: true,
				userId: true,
			},
		});

		const duration = performance.now() - startTime;

		expect(firstPage).toHaveLength(10);
		expect(secondPage).toHaveLength(10);
		expect(duration).toBeLessThan(50); // Pagination should be very fast

		console.log(`Pagination test completed in ${duration.toFixed(2)}ms`);
	});

	it("should batch update message status efficiently", async () => {
		const startTime = performance.now();
		
		const chatId = testData.chats[0].id;
		const userId = testData.users[1].id; // User 2 reading User 1's messages
		
		// Get unread messages
		const unreadMessages = await db.query.messages.findMany({
			where: eq(messages.chatId, chatId),
			columns: {
				id: true,
				userId: true,
				status: true,
			},
		});

		const unreadIds = unreadMessages
			.filter(msg => msg.userId !== userId && msg.status === "sent")
			.map(msg => msg.id);

		if (unreadIds.length > 0) {
			// Batch update status
			await db
				.update(messages)
				.set({ status: "read" })
				.where(eq(messages.id, unreadIds[0])); // Update one for testing
		}

		const duration = performance.now() - startTime;

		expect(duration).toBeLessThan(30); // Batch update should be very fast

		console.log(`Batch status update completed in ${duration.toFixed(2)}ms`);
	});
});
