"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	<PERSON>alogTitle,
	DialogTrigger,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { MessageCircle } from "lucide-react";
import { api } from "@/lib/trpc/react";
import { useSession } from "@/lib/auth/client";
import { useTranslations } from "next-intl";
import { cn } from "@/lib/utils";

export function ChatButton({
	userId,
	catId,
	className,
}: {
	userId: string;
	catId: string;
	className?: string;
}) {
	const [isOpen, setIsOpen] = useState(false);
	const [message, setMessage] = useState("");
	const [isSubmitting, setIsSubmitting] = useState(false);
	const router = useRouter();
	const { toast } = useToast();
	const { data: session } = useSession();
	const t = useTranslations("chat");
	const buttonT = useTranslations("buttons");

	// Use the optimized chat status query
	const { data: chatStatus, isLoading: isLoadingChatStatus } =
		api.messages.checkChatStatus.useQuery(
			{ catId },
			{
				enabled: !!session?.user,
			}
		);

	// Use tRPC mutation for creating a chat
	const createChat = api.messages.createChat.useMutation({
		onSuccess: (data) => {
			toast({
				title: t("successTitle"),
				description: t("successDescription"),
			});
			setIsOpen(false);
			router.push(`/profile/messages/${data.chatId}`);
		},
		onError: (error) => {
			toast({
				title: t("errorTitle"),
				description: error.message || t("errorDescription"),
				variant: "destructive",
			});
			setIsSubmitting(false);
		},
	});

	async function startChat() {
		if (!message.trim()) return;

		setIsSubmitting(true);

		try {
			await createChat.mutateAsync({
				recipientId: userId,
				catId,
				initialMessage: message,
			});
		} catch (error) {
			// Error is handled in the mutation callbacks
		}
	}

	// If there's an existing chat, show a button that redirects to it
	if (chatStatus?.chatExists && chatStatus?.chatId && !chatStatus?.isOwner) {
		return (
			<Button
				className={cn(
					"flex-1 cursor-pointer bg-teal-600 hover:bg-teal-700 text-white min-h-[44px]",
					className
				)}
				onClick={() => router.push(`/messages/${chatStatus.chatId}`)}
			>
				<MessageCircle className="h-4 w-4 mr-2" />
				{!isLoadingChatStatus && chatStatus.ownerName
					? t("continueConversationWith", {
							name: chatStatus.ownerName,
						})
					: t("continueConversation")}
			</Button>
		);
	}

	return (
		<Dialog open={isOpen} onOpenChange={setIsOpen}>
			<DialogTrigger asChild>
				<Button
					className={cn(
						"flex-1 bg-teal-600 hover:bg-teal-700 text-white min-h-[44px]",
						className
					)}
				>
					<MessageCircle className="h-4 w-4 mr-2" />
					{t("contactRescuer")}
				</Button>
			</DialogTrigger>
			<DialogContent>
				<DialogHeader>
					<DialogTitle>{t("startConversation")}</DialogTitle>
					<DialogDescription>
						{t("dialogDescription")}
					</DialogDescription>
				</DialogHeader>

				<div className="py-4">
					<Textarea
						placeholder={t("messagePlaceholder")}
						value={message}
						onChange={(e) => setMessage(e.target.value)}
						rows={4}
						className="resize-none"
					/>
				</div>

				<DialogFooter>
					<Button variant="outline" onClick={() => setIsOpen(false)}>
						{buttonT("cancel")}
					</Button>
					<Button
						onClick={startChat}
						disabled={
							!message.trim() ||
							isSubmitting ||
							createChat.isPending
						}
					>
						{isSubmitting || createChat.isPending
							? t("sending")
							: t("sendMessage")}
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
